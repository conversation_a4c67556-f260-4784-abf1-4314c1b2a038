/**
 * 页面管理器
 * 文件: js/pages/page-manager.js
 * 角色: 页面切换和状态管理，协调主页面和多订单页面的显示
 * 
 * @PAGE_MANAGER 页面管理器
 * 🏷️ 标签: @OTA_PAGE_MANAGER
 * 📝 说明: 负责页面切换逻辑和状态管理
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 页面管理器类
     * 管理不同页面的显示和隐藏
     */
    class PageManager {
        constructor() {
            this.logger = this.getLogger();
            this.router = null;
            
            // 页面配置
            this.config = {
                mainPageId: 'workspace',
                multiOrderPageId: 'multiOrderPanel',
                enableTransitions: true,
                transitionDuration: 300
            };

            // 页面状态
            this.state = {
                currentPage: null,
                previousPage: null,
                isTransitioning: false,
                pageHistory: []
            };

            // 页面元素缓存
            this.elements = {
                mainPage: null,
                multiOrderPage: null
            };

            this.logger.log('📄 页面管理器已初始化', 'info');
        }

        /**
         * 获取日志服务
         * @returns {Object} 日志服务实例
         */
        getLogger() {
            return window.getLogger?.() || {
                log: (message, level) => console.log(`[${level?.toUpperCase() || 'INFO'}] ${message}`),
                logError: (message, error) => console.error(`[ERROR] ${message}`, error)
            };
        }

        /**
         * 初始化页面管理器
         * @param {Object} router - 路由器实例
         */
        init(router) {
            this.router = router;
            
            // 缓存页面元素
            this.cachePageElements();
            
            // 注册路由
            this.registerRoutes();
            
            // 设置初始页面
            this.setInitialPage();
            
            this.logger.log('✅ 页面管理器初始化完成', 'success');
        }

        /**
         * 缓存页面元素
         */
        cachePageElements() {
            this.elements.mainPage = document.getElementById(this.config.mainPageId);

            // 多订单页面现在由 MultiOrder 模块动态创建，不再依赖静态HTML元素
            this.elements.multiOrderPage = document.getElementById(this.config.multiOrderPageId);

            if (!this.elements.mainPage) {
                this.logger.logError('主页面元素未找到', new Error(`Element not found: ${this.config.mainPageId}`));
            }

            // 多订单页面元素不存在是正常的，因为现在由 MultiOrder 模块动态管理
            if (!this.elements.multiOrderPage) {
                this.logger.log('多订单页面使用动态容器（由MultiOrder模块管理）', 'info');
            }

            this.logger.log('📋 页面元素已缓存', 'info');
        }

        /**
         * 注册路由
         */
        registerRoutes() {
            if (!this.router) {
                this.logger.logError('路由器未初始化', new Error('Router not initialized'));
                return;
            }

            // 主页面路由
            this.router.addRoute('/', () => {
                this.showMainPage();
            }, {
                title: 'OTA订单处理系统 - 主页',
                beforeEnter: () => this.canNavigateToMain(),
                afterEnter: () => this.onMainPageEntered()
            });

            // 多订单页面路由
            this.router.addRoute('/multi-order', (data) => {
                this.showMultiOrderPage(data);
            }, {
                title: 'OTA订单处理系统 - 多订单管理',
                beforeEnter: (path, data) => this.canNavigateToMultiOrder(data),
                afterEnter: () => this.onMultiOrderPageEntered()
            });

            this.logger.log('🛣️ 页面路由已注册', 'info');
        }

        /**
         * 设置初始页面
         */
        setInitialPage() {
            // 默认显示主页面
            this.showMainPage();
            this.state.currentPage = 'main';
        }

        /**
         * 显示主页面
         */
        async showMainPage() {
            if (this.state.isTransitioning) {
                this.logger.log('⏳ 页面切换中，跳过操作', 'warning');
                return;
            }

            try {
                this.state.isTransitioning = true;
                
                this.logger.log('🏠 切换到主页面', 'info');

                // 隐藏多订单页面 - 使用新的 MultiOrder API
                if (window.MultiOrder && window.MultiOrder.isVisible()) {
                    window.MultiOrder.hideUI();
                } else if (window.OTA?.multiOrderPage) {
                    window.OTA.multiOrderPage.hide();
                } else if (this.elements.multiOrderPage) {
                    await this.hidePage(this.elements.multiOrderPage);
                }

                // 显示主页面
                if (this.elements.mainPage) {
                    await this.showPage(this.elements.mainPage);
                }

                // 更新状态
                this.updatePageState('main');

                this.logger.log('✅ 主页面已显示', 'success');

            } catch (error) {
                this.logger.logError('显示主页面失败', error);
            } finally {
                this.state.isTransitioning = false;
            }
        }

        /**
         * 显示多订单页面
         * @param {Object} data - 传递给多订单页面的数据
         */
        async showMultiOrderPage(data = null) {
            if (this.state.isTransitioning) {
                this.logger.log('⏳ 页面切换中，跳过操作', 'warning');
                return;
            }

            try {
                this.state.isTransitioning = true;
                
                this.logger.log('📦 切换到多订单页面', 'info');

                // 隐藏主页面
                if (this.elements.mainPage) {
                    await this.hidePage(this.elements.mainPage);
                }

                // 更新状态
                this.updatePageState('multi-order');

                // 使用新的 MultiOrder 统一架构显示多订单页面
                if (window.MultiOrder) {
                    try {
                        // 使用统一的 MultiOrder API
                        const orders = data?.orders || [];

                        // 🧹 清理：移除演示数据逻辑
                        // 多订单界面只应在有真实订单数据时显示
                        if (orders.length === 0) {
                            this.logger.log('⚠️ 没有订单数据，不显示多订单界面', 'warning');
                            // 导航回主页面
                            this.showMainPage();
                            return;
                        }

                        const result = await window.MultiOrder.showUI(orders);
                        if (result !== false) {
                            this.logger.log('✅ 使用统一MultiOrder架构显示多订单页面', 'success');
                        } else {
                            throw new Error('MultiOrder.showUI 返回 false');
                        }
                    } catch (error) {
                        this.logger.logError('MultiOrder显示失败', error);
                        // 尝试显示空的多订单界面
                        await window.MultiOrder.showUI([]);
                    }
                } else if (window.OTA?.multiOrderPage) {
                    // 向后兼容：旧的 OTA 架构
                    await window.OTA.multiOrderPage.show(data);
                    this.logger.log('⚠️ 使用旧版OTA多订单架构', 'warning');
                } else {
                    // 最后的向后兼容：使用静态HTML元素
                    if (this.elements.multiOrderPage) {
                        await this.showPage(this.elements.multiOrderPage);
                    }
                    this.logger.log('⚠️ 使用静态HTML多订单面板，建议升级到统一架构', 'warning');
                }

                this.logger.log('✅ 多订单页面已显示', 'success');

            } catch (error) {
                this.logger.logError('显示多订单页面失败', error);
            } finally {
                this.state.isTransitioning = false;
            }
        }

        /**
         * 显示页面元素
         * @param {HTMLElement} element - 页面元素
         */
        async showPage(element) {
            if (!element) return;

            element.classList.remove('hidden');
            element.style.display = 'flex';
            element.setAttribute('aria-hidden', 'false');

            // 添加过渡效果
            if (this.config.enableTransitions) {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';
                
                // 强制重绘
                element.offsetHeight;
                
                element.style.transition = `opacity ${this.config.transitionDuration}ms ease, transform ${this.config.transitionDuration}ms ease`;
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';

                // 等待过渡完成
                await new Promise(resolve => setTimeout(resolve, this.config.transitionDuration));
            }
        }

        /**
         * 隐藏页面元素
         * @param {HTMLElement} element - 页面元素
         */
        async hidePage(element) {
            if (!element) return;

            // 添加过渡效果
            if (this.config.enableTransitions) {
                element.style.transition = `opacity ${this.config.transitionDuration}ms ease, transform ${this.config.transitionDuration}ms ease`;
                element.style.opacity = '0';
                element.style.transform = 'translateY(-20px)';

                // 等待过渡完成
                await new Promise(resolve => setTimeout(resolve, this.config.transitionDuration));
            }

            element.classList.add('hidden');
            element.style.display = 'none';
            element.setAttribute('aria-hidden', 'true');
        }

        /**
         * 更新页面状态
         * @param {string} currentPage - 当前页面
         */
        updatePageState(currentPage) {
            this.state.previousPage = this.state.currentPage;
            this.state.currentPage = currentPage;
            
            // 添加到历史记录
            this.state.pageHistory.push({
                page: currentPage,
                timestamp: Date.now()
            });

            // 限制历史记录长度
            if (this.state.pageHistory.length > 10) {
                this.state.pageHistory = this.state.pageHistory.slice(-10);
            }
        }

        /**
         * 检查是否可以导航到主页面
         * @returns {boolean} 是否可以导航
         */
        canNavigateToMain() {
            // 可以添加权限检查等逻辑
            return true;
        }

        /**
         * 检查是否可以导航到多订单页面
         * @param {Object} data - 路由数据
         * @returns {boolean} 是否可以导航
         */
        canNavigateToMultiOrder(data) {
            // 🔐 首先检查用户是否已登录
            let isLoggedIn = false;
            try {
                const appState = window.getAppState?.();
                isLoggedIn = appState?.get('auth.isLoggedIn') || false;
                
                if (!isLoggedIn) {
                    this.logger.log('❌ 用户未登录，无法访问多订单页面，将重定向到登录界面', 'warning');
                    
                    // 重定向到主页面（显示登录界面）
                    setTimeout(() => {
                        if (this.router) {
                            this.router.navigate('/');
                        } else {
                            window.location.hash = '/';
                        }
                    }, 100);
                    
                    return false;
                }
            } catch (error) {
                this.logger.logError('检查登录状态时发生错误', error);
                return false;
            }

            // 检查数据格式
            if (data && data.orders && !Array.isArray(data.orders)) {
                this.logger.log('❌ 多订单数据格式错误', 'warning');
                return false;
            }

            // 🧹 新增：检查是否有有效的订单数据
            const orders = data?.orders || [];
            if (orders.length === 0) {
                this.logger.log('❌ 没有订单数据，不允许访问多订单页面', 'warning');
                // 重定向到主页面
                setTimeout(() => {
                    if (this.router) {
                        this.router.navigate('/');
                    } else {
                        window.location.hash = '/';
                    }
                }, 100);
                return false;
            }

            // 验证订单数据的基本结构
            const isValidOrder = (order) => {
                return order && 
                       typeof order.id === 'string' && 
                       typeof order.customerName === 'string';
            };

            const validOrders = orders.filter(isValidOrder);
            if (validOrders.length === 0) {
                this.logger.log('❌ 没有有效的订单数据，不允许访问多订单页面', 'warning');
                // 重定向到主页面
                setTimeout(() => {
                    if (this.router) {
                        this.router.navigate('/');
                    } else {
                        window.location.hash = '/';
                    }
                }, 100);
                return false;
            }

            // 检查MultiOrder模块是否可用
            if (!window.MultiOrder) {
                this.logger.log('❌ MultiOrder模块不可用', 'warning');
                return false;
            }

            this.logger.log(`✅ 多订单页面访问权限验证通过，包含 ${validOrders.length} 个有效订单`, 'success');
            return true;
        }

        /**
         * 主页面进入后的回调
         */
        onMainPageEntered() {
            // 可以添加主页面特定的初始化逻辑
            this.logger.log('🏠 主页面已激活', 'info');
        }

        /**
         * 多订单页面进入后的回调
         */
        onMultiOrderPageEntered() {
            // 可以添加多订单页面特定的初始化逻辑
            this.logger.log('📦 多订单页面已激活', 'info');
        }

        /**
         * 获取当前页面
         * @returns {string} 当前页面标识
         */
        getCurrentPage() {
            return this.state.currentPage;
        }

        /**
         * 获取上一个页面
         * @returns {string} 上一个页面标识
         */
        getPreviousPage() {
            return this.state.previousPage;
        }

        /**
         * 检查是否正在切换页面
         * @returns {boolean} 是否正在切换
         */
        isTransitioning() {
            return this.state.isTransitioning;
        }

        /**
         * 获取页面历史记录
         * @returns {Array} 页面历史记录
         */
        getPageHistory() {
            return [...this.state.pageHistory];
        }

        /**
         * 销毁页面管理器
         */
        destroy() {
            // 清理状态
            this.state = {
                currentPage: null,
                previousPage: null,
                isTransitioning: false,
                pageHistory: []
            };

            // 清理元素缓存
            this.elements = {
                mainPage: null,
                multiOrderPage: null
            };

            this.router = null;

            this.logger.log('🗑️ 页面管理器已销毁', 'info');
        }
    }

    // 创建全局页面管理器实例
    const pageManager = new PageManager();

    // 暴露到OTA命名空间
    window.OTA.PageManager = PageManager;
    window.OTA.pageManager = pageManager;

    // 向后兼容
    window.pageManager = pageManager;

    console.log('✅ 页面管理器已加载');

})();
