#!/bin/bash

echo "🚀 启动OTA订单处理系统本地服务器..."
echo ""
echo "选择启动方式:"
echo "1. Python HTTP服务器 (推荐)"
echo "2. Node.js HTTP服务器"
echo "3. PHP内置服务器"
echo ""
read -p "请选择 (1-3): " choice

case $choice in
    1)
        echo ""
        echo "🐍 启动Python HTTP服务器..."
        echo "服务器地址: http://localhost:8000"
        echo "按 Ctrl+C 停止服务器"
        echo ""
        python3 -m http.server 8000 || python -m http.server 8000
        ;;
    2)
        echo ""
        echo "📦 启动Node.js HTTP服务器..."
        echo "服务器地址: http://localhost:8080"
        echo "按 Ctrl+C 停止服务器"
        echo ""
        npx http-server -p 8080
        ;;
    3)
        echo ""
        echo "🐘 启动PHP内置服务器..."
        echo "服务器地址: http://localhost:8000"
        echo "按 Ctrl+C 停止服务器"
        echo ""
        php -S localhost:8000
        ;;
    *)
        echo ""
        echo "🐍 默认启动Python HTTP服务器..."
        echo "服务器地址: http://localhost:8000"
        echo "按 Ctrl+C 停止服务器"
        echo ""
        python3 -m http.server 8000 || python -m http.server 8000
        ;;
esac

echo ""
echo "服务器已停止"
