/**
 * 主CSS文件 - 导入所有模块化CSS文件
 * 按照优先级顺序导入：基础 -> 布局 -> 组件 -> 页面 -> 主题
 */

/* =================================
   基础层 - 变量、重置、工具类
   ================================= */
@import url('./base/variables.css');
@import url('./base/reset.css');
@import url('./base/utilities.css');

/* =================================
   布局层 - 网格、头部、容器
   ================================= */
@import url('./layout/grid.css');
@import url('./layout/header.css');

/* =================================
   组件层 - 可重用组件
   ================================= */
@import url('./components/buttons.css');
@import url('./components/forms.css');
@import url('./components/cards.css');
@import url('./components/animations.css');

/* =================================
   页面层 - 特定页面样式
   ================================= */
@import url('./pages/workspace.css');

/* =================================
   多订单模块样式 - 已移至 modules/multi-order.js
   ================================= */
/* Multi-order styles are now embedded in the module itself for better encapsulation */

/* =================================
   全局应用样式
   ================================= */
#app {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  overflow: hidden;
  box-sizing: border-box;
}

/* =================================
   价格管理器手动编辑模式样式
   ================================= */
.price-conversion-display {
  margin-top: 10px;
  padding: 12px;
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
}

.price-conversion-display.manual-edit-mode {
  background: #fff3cd;
  border-color: #ffc107;
  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.2);
}

.conversion-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.conversion-arrow {
  color: #6c757d;
  font-weight: bold;
}

.conversion-rate {
  color: #6c757d;
  font-size: 12px;
  margin-bottom: 8px;
}

.conversion-controls {
  text-align: right;
}

.reset-exchange-btn {
  background: #17a2b8;
  color: white;
  border: none;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.reset-exchange-btn:hover {
  background: #138496;
}

.manual-edit-indicator {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* =================================
   过渡和动画增强 - 性能优化版本
   ================================= */
/* 【性能优化】移除全局过渡，改为选择性应用 */
.transition-enabled {
  transition: color var(--transition-fast), 
              background-color var(--transition-fast), 
              border-color var(--transition-fast),
              box-shadow var(--transition-fast);
}

/* 常用交互元素的过渡效果 */
button, 
.btn,
input[type="text"]:hover,
input[type="email"]:hover,
input[type="tel"]:hover,
select:hover,
.form-group input:focus,
.form-group select:focus {
  transition: all var(--transition-fast);
}

/* 禁用拖拽时的过渡 */
.resizing * {
  transition: none !important;
}

/* =================================
   可访问性增强
   ================================= */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --text-primary: #000000;
    --text-secondary: #333333;
  }
}

/* =================================
   历史订单面板样式
   ================================= */
/* 历史订单面板基础样式 */
.history-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: var(--overlay-backdrop);
    z-index: var(--z-modal);
    display: none;
    align-items: center;
    justify-content: center;
    padding: 16px;
    box-sizing: border-box;
}

.history-panel:not(.hidden) {
    display: flex !important;
}

.history-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--overlay-backdrop);
    z-index: 1;
}

.history-content {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    width: 90vw;
    max-height: 90vh;
    background: var(--bg-tertiary);
    -webkit-backdrop-filter: var(--blur-glass);
    backdrop-filter: var(--blur-glass);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* 历史订单面板移动端优化 */
@media (max-width: 768px) {
    .history-content {
        width: 95vw;
        max-height: 95vh;
        margin: 0;
    }

    .history-panel {
        padding: 8px;
    }
}

@media (max-width: 480px) {
    .history-content {
        width: 98vw;
        max-height: 98vh;
        border-radius: 12px;
    }

    .history-panel {
        padding: 4px;
    }
}

/* 历史订单子元素样式 */
.history-header {
    background: var(--brand-gradient);
    color: var(--color-white);
    padding: var(--spacing-md) var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.history-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.history-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.history-search {
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    flex-shrink: 0;
}

.search-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.search-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.search-group label {
    font-size: var(--font-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.search-group input {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.search-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

.history-stats {
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    flex-shrink: 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-md);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-sm);
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-color);
}

.stat-label {
    display: block;
    font-size: var(--font-xs);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.stat-value {
    display: block;
    font-size: var(--font-lg);
    font-weight: 600;
    color: var(--color-primary);
}

.history-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.list-header {
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.list-title {
    font-weight: 600;
    color: var(--text-primary);
}

.list-count {
    font-size: var(--font-sm);
    color: var(--text-secondary);
}

.list-container {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md);
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.empty-text {
    font-size: var(--font-base);
    opacity: 0.7;
}

.history-item {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-md);
    overflow: hidden;
    transition: all var(--transition-fast);
}

.history-item:hover {
    border-color: var(--color-primary);
    box-shadow: var(--shadow-md);
}

.history-item-header {
    background: var(--bg-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-sm);
}

.history-item-content {
    padding: var(--spacing-md);
}

.history-item-content p {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-sm);
    line-height: 1.4;
}

/* 流式布局样式 */
.history-item-content.flow-layout {
    padding: var(--spacing-md);
    line-height: 1.8;
    font-size: var(--font-sm);
    color: var(--text-primary);
}

.history-item-content.flow-layout .field-item {
    display: inline;
    margin-right: var(--spacing-xs);
    white-space: nowrap;
}

.history-item-content.flow-layout .field-item:not(:last-child)::after {
    content: " • ";
    color: var(--text-secondary);
    margin-left: var(--spacing-xs);
    margin-right: var(--spacing-xs);
    font-weight: normal;
}

.history-item-content.flow-layout .field-label {
    font-weight: 600;
    color: var(--text-primary);
}

.history-item-content.flow-layout .field-value {
    color: var(--text-secondary);
    font-weight: normal;
}

/* 错误信息在流式布局中的特殊处理 */
.history-item-content.flow-layout p {
    margin: var(--spacing-sm) 0 0 0;
    display: block;
    font-size: var(--font-sm);
    line-height: 1.4;
}

/* 历史订单移动端子元素优化 */
@media (max-width: 768px) {
    .history-header {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .history-header h3 {
        font-size: 1rem;
    }

    .search-grid {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-sm);
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
    }

    .list-header {
        padding: var(--spacing-sm) var(--spacing-md);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }

    .history-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
}

@media (max-width: 480px) {
    .history-search,
    .history-stats,
    .list-container {
        padding: var(--spacing-sm);
    }

    .stats-grid {
        grid-template-columns: 1fr 1fr;
    }

    .search-actions {
        justify-content: stretch;
    }

    .search-actions .btn {
        flex: 1;
    }
}

/* 打印样式 */
@media print {
  * {
    background: var(--color-white) !important;
    color: var(--color-gray-900) !important;
    box-shadow: none !important;
  }

  .no-print {
    display: none !important;
  }

  .btn,
  .header-controls,
  .status-bar {
    display: none !important;
  }
}