@echo off
echo 🚀 启动OTA订单处理系统本地服务器...
echo.
echo 选择启动方式:
echo 1. Python HTTP服务器 (推荐)
echo 2. Node.js HTTP服务器
echo 3. PHP内置服务器
echo.
set /p choice="请选择 (1-3): "

if "%choice%"=="1" goto python
if "%choice%"=="2" goto nodejs  
if "%choice%"=="3" goto php
goto python

:python
echo.
echo 🐍 启动Python HTTP服务器...
echo 服务器地址: http://localhost:8000
echo 按 Ctrl+C 停止服务器
echo.
python -m http.server 8000
goto end

:nodejs
echo.
echo 📦 启动Node.js HTTP服务器...
echo 服务器地址: http://localhost:8080
echo 按 Ctrl+C 停止服务器
echo.
npx http-server -p 8080
goto end

:php
echo.
echo 🐘 启动PHP内置服务器...
echo 服务器地址: http://localhost:8000
echo 按 Ctrl+C 停止服务器
echo.
php -S localhost:8000
goto end

:end
echo.
echo 服务器已停止
pause
