<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Order System Test - 多订单系统测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn.secondary {
            background: #6c757d;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .sample-orders {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status.info { background: #d1ecf1; color: #0c5460; }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
        }
        
        /* Dark theme */
        [data-theme="dark"] {
            background: #1a1a1a;
            color: #e0e0e0;
        }
        
        [data-theme="dark"] .test-container {
            background: #2d2d2d;
            color: #e0e0e0;
        }
        
        [data-theme="dark"] .test-section {
            background: #3d3d3d;
        }
        
        [data-theme="dark"] .sample-orders {
            background: #404040;
            color: #e0e0e0;
        }
    </style>
</head>
<body>
    <div class="theme-toggle">
        <button class="btn secondary" onclick="toggleTheme()">🌙 切换主题</button>
    </div>
    
    <div class="test-container">
        <h1>🔢 多订单系统测试页面</h1>
        <p>此页面用于测试多订单显示功能的修复效果</p>
        
        <div id="status" class="status info">
            📊 系统状态：正在初始化...
        </div>
        
        <!-- Test Section 1: Module Loading -->
        <div class="test-section">
            <h3>📦 模块加载测试</h3>
            <p>测试多订单模块是否正确加载和初始化</p>
            <button class="btn" onclick="testModuleLoading()">测试模块加载</button>
            <button class="btn secondary" onclick="testInitialization()">测试初始化</button>
        </div>
        
        <!-- Test Section 2: Sample Multi-Orders -->
        <div class="test-section">
            <h3>🎯 多订单检测测试</h3>
            <p>使用示例数据测试多订单检测和显示功能</p>
            
            <div class="sample-orders">订单1：
客户：张三
电话：13800138000
从广州白云机场到珠江新城
时间：2024-12-25 10:00
乘客：2人，行李：3件

订单2：
客户：李四  
电话：13900139000
从深圳机场到罗湖口岸
时间：2024-12-25 14:00
乘客：1人，行李：2件

订单3：
客户：王五
电话：13700137000  
从香港机场到中环
时间：2024-12-25 18:00
乘客：3人，行李：4件</div>
            
            <button class="btn" onclick="testSampleOrders()">测试示例订单</button>
            <button class="btn success" onclick="testChineseOrders()">测试中文订单</button>
        </div>
        
        <!-- Test Section 3: UI Display -->
        <div class="test-section">
            <h3>🎨 UI显示测试</h3>
            <p>测试多订单对话框的显示效果和交互功能</p>
            <button class="btn" onclick="testUIDisplay()">直接显示UI</button>
            <button class="btn secondary" onclick="testDarkTheme()">测试暗黑主题</button>
        </div>
        
        <!-- Test Section 4: Integration -->
        <div class="test-section">
            <h3>🔗 集成测试</h3>
            <p>测试与主系统的集成情况</p>
            <button class="btn" onclick="testEventIntegration()">测试事件集成</button>
            <button class="btn secondary" onclick="testFullWorkflow()">测试完整流程</button>
        </div>
        
        <!-- Results -->
        <div id="results" class="test-section" style="display: none;">
            <h3>📋 测试结果</h3>
            <div id="test-log"></div>
        </div>
    </div>

    <!-- Load the MultiOrder module -->
    <script src="modules/multi-order.js"></script>
    
    <script>
        // Test utilities
        let testResults = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            
            console.log(logEntry);
            testResults.push({ message: logEntry, type });
            
            updateStatus(message, type);
            updateTestLog();
        }
        
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${type}`;
            statusEl.innerHTML = `📊 ${message}`;
        }
        
        function updateTestLog() {
            const logEl = document.getElementById('test-log');
            const resultsEl = document.getElementById('results');
            
            logEl.innerHTML = testResults.map(result => 
                `<div class="status ${result.type}">${result.message}</div>`
            ).join('');
            
            resultsEl.style.display = 'block';
        }
        
        // Theme toggle
        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.body.setAttribute('data-theme', newTheme);
            
            // Notify multi-order module of theme change
            const event = new CustomEvent('themeChanged', {
                detail: { theme: newTheme }
            });
            document.dispatchEvent(event);
            
            log(`主题已切换到: ${newTheme}`, 'info');
        }
        
        // Test functions
        async function testModuleLoading() {
            log('开始测试模块加载...', 'info');
            
            try {
                if (typeof window.MultiOrder === 'undefined') {
                    throw new Error('MultiOrder模块未加载');
                }
                
                log('✅ MultiOrder模块已成功加载', 'success');
                
                // Test public API
                const api = ['initialize', 'detect', 'process', 'showUI', 'hideUI'];
                const missingMethods = api.filter(method => typeof window.MultiOrder[method] !== 'function');
                
                if (missingMethods.length > 0) {
                    throw new Error(`缺少方法: ${missingMethods.join(', ')}`);
                }
                
                log('✅ 所有必需的API方法都可用', 'success');
                
            } catch (error) {
                log(`❌ 模块加载测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testInitialization() {
            log('开始测试初始化...', 'info');
            
            try {
                const result = await window.MultiOrder.initialize();
                
                if (result) {
                    log('✅ 初始化成功', 'success');
                    
                    // Test state
                    const state = window.MultiOrder.getState();
                    log(`📊 模块状态: ${JSON.stringify(state)}`, 'info');
                    
                } else {
                    throw new Error('初始化返回false');
                }
                
            } catch (error) {
                log(`❌ 初始化测试失败: ${error.message}`, 'error');
            }
        }
        
        function testSampleOrders() {
            log('开始测试示例订单检测...', 'info');
            
            const sampleText = `订单1：
客户：张三
电话：13800138000
从广州白云机场到珠江新城
时间：2024-12-25 10:00
乘客：2人，行李：3件

订单2：
客户：李四  
电话：13900139000
从深圳机场到罗湖口岸
时间：2024-12-25 14:00
乘客：1人，行李：2件`;
            
            try {
                const detection = window.MultiOrder.detect(sampleText);
                log(`🔍 检测结果: ${JSON.stringify(detection, null, 2)}`, 'info');
                
                if (detection.isMultiOrder) {
                    log(`✅ 成功检测到多订单: ${detection.orders.length}个订单`, 'success');
                    
                    // Process and show
                    const processed = window.MultiOrder.process(detection.orders);
                    window.MultiOrder.showUI(processed);
                    
                } else {
                    log('⚠️ 未检测到多订单', 'error');
                }
                
            } catch (error) {
                log(`❌ 示例订单测试失败: ${error.message}`, 'error');
            }
        }
        
        function testChineseOrders() {
            log('开始测试中文订单处理...', 'info');
            
            const chineseText = `第一个订单：
顾客姓名：王小明
联系方式：+86 138 0013 8000
出发地：北京首都国际机场T3航站楼
目的地：北京国贸CBD
预约时间：2024年12月25日上午10点30分
人数：2成人，1儿童
行李：2个大箱子，1个背包

第二个订单：
顾客姓名：李大华
联系电话：+86 139 0013 9000  
上车地点：上海浦东国际机场
下车地点：上海外滩
服务时间：2024年12月25日下午2点
乘客：1人
行李：1个拉杆箱

第三个订单：
客户：陈小红
手机：+86 137 0013 7000
起点：广州白云机场
终点：广州珠江新城
日期时间：2024-12-25 18:00
乘客数量：4人
行李数量：3件`;
            
            try {
                const detection = window.MultiOrder.detect(chineseText);
                log(`🔍 中文检测结果: 找到${detection.orders?.length || 0}个订单`, 'info');
                
                if (detection.isMultiOrder) {
                    log(`✅ 成功检测中文多订单`, 'success');
                    
                    const processed = window.MultiOrder.process(detection.orders);
                    window.MultiOrder.showUI(processed);
                    
                } else {
                    log('⚠️ 中文多订单检测失败', 'error');
                }
                
            } catch (error) {
                log(`❌ 中文订单测试失败: ${error.message}`, 'error');
            }
        }
        
        function testUIDisplay() {
            log('开始测试UI显示...', 'info');
            
            try {
                // Create mock orders for display testing
                const mockOrders = [
                    {
                        id: 'test_1',
                        content: '测试订单1 - 这是一个用于测试显示效果的示例订单，包含中文字符和较长的描述文本。',
                        status: 'ready',
                        selected: false
                    },
                    {
                        id: 'test_2', 
                        content: '测试订单2 - Another test order with mixed content 中英文混合内容测试',
                        status: 'detected',
                        selected: true
                    },
                    {
                        id: 'test_3',
                        content: '测试订单3 - 短订单',
                        status: 'pending',
                        selected: false
                    }
                ];
                
                const result = window.MultiOrder.showUI(mockOrders);
                
                if (result) {
                    log('✅ UI显示成功', 'success');
                } else {
                    log('❌ UI显示失败', 'error');
                }
                
            } catch (error) {
                log(`❌ UI显示测试失败: ${error.message}`, 'error');
            }
        }
        
        function testEventIntegration() {
            log('开始测试事件集成...', 'info');
            
            try {
                // Simulate a multi-order detection event
                const mockResult = {
                    isMultiOrder: true,
                    confidence: 0.85,
                    orders: [
                        { content: '模拟订单1：客户张三，电话138xxx，机场接送', status: 'detected' },
                        { content: '模拟订单2：客户李四，电话139xxx，酒店接送', status: 'detected' }
                    ],
                    orderCount: 2
                };
                
                const event = new CustomEvent('multiOrderDetected', {
                    detail: {
                        multiOrderResult: mockResult,
                        orderText: '模拟的多订单文本内容'
                    }
                });
                
                document.dispatchEvent(event);
                log('✅ 多订单检测事件已触发', 'success');
                
            } catch (error) {
                log(`❌ 事件集成测试失败: ${error.message}`, 'error');
            }
        }
        
        function testFullWorkflow() {
            log('开始测试完整工作流程...', 'info');
            
            try {
                const fullText = `完整测试订单批次：

订单A：张经理的专车服务
客户：张经理
联系：138-0013-8001
路线：广州南站 → 天河区CBD
时间：明天上午9:00
人数：1人，行李2件
备注：需要商务车

订单B：李总的机场接送
顾客：李总
电话：139-0013-9002
从广州白云机场T2到越秀区办公楼
预约：明天下午2:30
乘客：2人，行李：1个大箱子
要求：准时，司机穿正装

订单C：王小姐的购物行程
客户姓名：王小姐
手机号码：137-0013-7003
起点：天河城
终点：太古汇 → 正佳广场 → 回天河城
服务时间：后天全天
人员：1人
需求：熟悉路线的司机`;
                
                // Step 1: Detection
                log('步骤1：执行多订单检测', 'info');
                const detection = window.MultiOrder.detect(fullText);
                
                if (!detection.isMultiOrder) {
                    throw new Error('检测未识别为多订单');
                }
                
                log(`✅ 检测成功：${detection.orders.length}个订单，置信度${Math.round(detection.confidence * 100)}%`, 'success');
                
                // Step 2: Processing  
                log('步骤2：处理订单数据', 'info');
                const processed = window.MultiOrder.process(detection.orders);
                
                // Step 3: Display
                log('步骤3：显示多订单界面', 'info');
                const shown = window.MultiOrder.showUI(processed);
                
                if (shown) {
                    log('✅ 完整工作流程测试成功！', 'success');
                } else {
                    throw new Error('UI显示失败');
                }
                
            } catch (error) {
                log(`❌ 完整工作流程测试失败: ${error.message}`, 'error');
            }
        }
        
        function testDarkTheme() {
            log('测试暗黑主题支持...', 'info');
            
            // Toggle to dark theme
            document.body.setAttribute('data-theme', 'dark');
            
            // Test UI in dark theme
            testUIDisplay();
            
            log('✅ 暗黑主题测试完成', 'success');
        }
        
        // Initialize when page loads
        window.addEventListener('load', async () => {
            log('页面加载完成，开始自动测试...', 'info');
            
            // Wait a bit for module to initialize
            setTimeout(async () => {
                await testModuleLoading();
                await testInitialization();
                
                log('🎉 自动测试完成，可以手动运行其他测试', 'success');
            }, 100);
        });
        
        // Listen for multi-order events
        document.addEventListener('multiOrderDetected', (event) => {
            log('收到多订单检测事件', 'info');
        });
        
        document.addEventListener('multiOrderInitialized', (event) => {
            log('多订单模块初始化完成', 'success');
        });
        
    </script>
</body>
</html>