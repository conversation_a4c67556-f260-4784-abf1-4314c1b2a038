<!-- build-stamp: will be replaced at deploy; version 2.0.3 -->
<!--
@FIELD_FORMAT: camelCase - 待转换为 snake_case
@FIELDS_USED: customerName, customerContact, customerEmail, flightInfo, otaPrice, otaReferenceNumber,
              pickupDate, pickupTime, passengerCount, luggageCount, subCategoryId, carTypeId,
              drivingRegionId, extraRequirement, dropoff
@CONVERSION_TARGET: customer_name, customer_contact, customer_email, flight_info, ota_price,
                   ota_reference_number, date, time, passenger_number, luggage_number,
                   sub_category_id, car_type_id, driving_region_id, extra_requirement, destination
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="header.pageTitle">OTA订单处理系统 - GoMyHire Integration</title>
    
    <!-- 【性能优化】关键CSS内联，减少首屏渲染阻塞 -->
    <style>
        /* 最小化关键渲染路径CSS - 仅保留必要的基础样式 */
        #app {
            display: flex;
            flex-direction: column;
            height: 100vh;
            background: #f8fafc;
        }
        .hidden {
            display: none !important;
        }

        /* 登录状态控制 - 核心显示逻辑 */
        body:not(.logged-in) #workspace {
            display: none !important;
        }
        body.logged-in #loginPanel {
            display: none !important;
        }
        body.logged-in #workspace {
            display: flex !important;
        }
        body:not(.logged-in) #loginPanel {
            display: flex !important;
        }

        /* 防止布局抖动 */
        .workspace {
            min-height: 0;
        }
        .login-panel {
            display: flex;
            align-items: center;
            justify-content: center;
            flex: 1;
        }

        /* CSS加载中的占位样式 */
        .css-loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 8px;
            z-index: 9999;
            font-family: Arial, sans-serif;
        }
    </style>
    
    <!-- 🚀 JavaScript动态CSS加载器 - 完全规避CORS限制 -->
    <script>
        // 高效CSS动态加载器
        class CSSLoader {
            constructor() {
                this.loadedFiles = new Set();
                this.loadingPromises = new Map();
                this.showLoadingIndicator();
            }

            showLoadingIndicator() {
                const indicator = document.createElement('div');
                indicator.id = 'css-loading-indicator';
                indicator.className = 'css-loading';
                indicator.innerHTML = '🎨 正在加载样式文件...<br><small>请稍候</small>';
                document.body.appendChild(indicator);
            }

            hideLoadingIndicator() {
                const indicator = document.getElementById('css-loading-indicator');
                if (indicator) {
                    indicator.remove();
                }
            }

            async loadCSS(url) {
                if (this.loadedFiles.has(url)) {
                    return Promise.resolve();
                }

                if (this.loadingPromises.has(url)) {
                    return this.loadingPromises.get(url);
                }

                const loadPromise = this._loadCSSFile(url);
                this.loadingPromises.set(url, loadPromise);

                try {
                    await loadPromise;
                    this.loadedFiles.add(url);
                } catch (error) {
                    console.warn(`CSS加载失败: ${url}`, error);
                } finally {
                    this.loadingPromises.delete(url);
                }

                return loadPromise;
            }

            async _loadCSSFile(url) {
                // 优先使用XMLHttpRequest，在file://协议下更可靠
                try {
                    const cssText = await this._loadWithXHR(url);
                    this._injectCSS(cssText, url);
                } catch (xhrError) {
                    // 回退到动态link标签
                    await this._loadWithLink(url);
                }
            }

            _loadWithXHR(url) {
                return new Promise((resolve, reject) => {
                    const xhr = new XMLHttpRequest();
                    xhr.open('GET', url, true);
                    xhr.onreadystatechange = function() {
                        if (xhr.readyState === 4) {
                            if (xhr.status === 200 || xhr.status === 0) { // status 0 for file://
                                resolve(xhr.responseText);
                            } else {
                                reject(new Error(`XHR failed: ${xhr.status}`));
                            }
                        }
                    };
                    xhr.onerror = () => reject(new Error('XHR network error'));
                    xhr.send();
                });
            }

            _loadWithLink(url) {
                return new Promise((resolve, reject) => {
                    const link = document.createElement('link');
                    link.rel = 'stylesheet';
                    link.href = url;
                    link.onload = () => resolve();
                    link.onerror = () => reject(new Error('Link load failed'));
                    document.head.appendChild(link);
                });
            }

            _injectCSS(cssText, url) {
                const style = document.createElement('style');
                style.setAttribute('data-source', url);
                style.textContent = cssText;
                document.head.appendChild(style);
            }

            async loadMultiple(urls) {
                const promises = urls.map(url => this.loadCSS(url));
                await Promise.allSettled(promises);
                this.hideLoadingIndicator();
            }
        }

        // 立即初始化并开始加载
        (function() {
            const cssLoader = new CSSLoader();

            const loadCSS = async () => {
                // 按优先级顺序加载CSS文件
                const cssFiles = [
                    'css/base/variables.css',
                    'css/base/reset.css',
                    'css/base/typography.css',
                    'css/layout/header.css',
                    'css/layout/main.css',
                    'css/layout/footer.css',
                    'css/components/forms.css',
                    'css/components/buttons.css',
                    'css/components/panels.css',
                    'css/components/modals.css',
                    'css/themes/dark.css',
                    'css/themes/responsive.css'
                ];

                await cssLoader.loadMultiple(cssFiles);

                // 通知CSS加载完成
                document.dispatchEvent(new CustomEvent('cssLoaded'));
                console.log('✅ CSS动态加载完成');
            };

            // 立即开始加载，不等待DOMContentLoaded
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', loadCSS);
            } else {
                loadCSS();
            }
        })();
    </script>

    <!-- 保留原始CSS链接作为最终备用 -->
    <link rel="stylesheet" href="css/main.css">
    <!-- 多订单模块样式已移至 modules/multi-order.js 内部 -->
    <!-- 图标已移除，使用浏览器默认图标 -->
</head>
<body>
    <div id="app">
        <!-- 顶部导航栏 -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <span class="title-icon">🚗</span>
                    <span data-i18n="header.title">OTA订单处理系统</span>
                </h1>
                <div class="header-controls">
                    <div class="persistent-email hidden" id="persistentEmailContainer" aria-hidden="true">
                        <label for="persistentEmail" data-i18n="header.defaultEmail">默认邮箱:</label>
                        <input type="email" id="persistentEmail" data-i18n="header.defaultEmailPlaceholder" placeholder="设置默认客户邮箱" data-i18n-title="header.defaultEmailTooltip" title="设置默认客户邮箱，当AI解析无法获取邮箱时自动使用">
                        <button type="button" id="saveEmailBtn" class="btn btn-icon" data-i18n-title="common.save" title="保存邮箱">💾</button>
                    </div>
                    <div class="user-info hidden" id="userInfo" aria-hidden="true">
                        <span id="currentUser"></span>
                        <button type="button" id="historyBtn" class="btn btn-outline" data-i18n="header.historyOrders">历史订单</button>
                        <button type="button" id="logoutBtn" class="btn btn-outline" data-i18n="header.logout">退出登录</button>
                    </div>
                    <div class="theme-toggle">
                        <select id="languageSelect" class="language-select" data-i18n-title="header.language" title="选择语言">
                            <option value="zh" data-i18n="header.languageZh">中文</option>
                            <option value="en" data-i18n="header.languageEn">English</option>
                        </select>
                        <button type="button" id="themeToggle" class="btn btn-icon" data-i18n-title="header.toggleTheme" title="切换主题">🌙</button>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主要内容区 -->
        <main class="main-content">
            <!-- 登录面板 -->
            <div id="loginPanel" class="login-panel">
                <div class="login-card">
                    <h2 data-i18n="login.title">系统登录</h2>
                    <form id="loginForm">
                        <div class="form-group">
                            <label for="email" data-i18n="login.email">邮箱</label>
                            <input type="email" id="email" value="" data-i18n="login.emailPlaceholder" placeholder="请输入邮箱地址" required>
                        </div>
                        <div class="form-group">
                            <label for="password" data-i18n="login.password">密码</label>
                            <input type="password" id="password" value="" data-i18n="login.passwordPlaceholder" placeholder="请输入密码" required>
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="rememberMe" checked>
                                <span class="checkbox-text" data-i18n="login.rememberMe">保持登录</span>
                            </label>
                        </div>
                        <div class="login-actions">
                            <button type="submit" class="btn btn-primary" id="loginBtn">
                                <span class="btn-text" data-i18n="login.loginButton">登录</span>
                                <span class="loading-spinner hidden">⏳</span>
                            </button>
                            <button type="button" class="btn btn-outline btn-sm hidden" id="clearSavedBtn" data-i18n="login.clearSaved">清除保存的账号</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 工作区 -->
            <div id="workspace" class="workspace hidden" aria-hidden="true">
                <!-- 三列布局容器 -->
                <form id="orderForm" class="three-column-layout">
                    <!-- 左列：订单输入 + 行程信息 -->
                    <div class="column-left column-mobile">
                        <!-- 订单输入板块 -->
                        <section class="panel compact-card" data-panel="order-input" role="region" aria-label="订单输入区域" tabindex="0">


                            <div class="panel-content compact-inline-layout">
                                <div class="form-group">
                                    <label for="orderInput" data-i18n="input.orderDescription">订单描述</label>
                                    <textarea
                                        id="orderInput"
                                        data-i18n="input.placeholder"
                                        placeholder="请输入订单描述文本，系统将自动解析订单信息..."
                                        rows="3"
                                    ></textarea>
                                </div>
                                <div class="form-group">
                                    <div class="compact-upload-container">
                                        <button type="button" class="btn-compact-upload" id="imageUploadButton" title="上传图片" aria-label="上传图片">
                                            <span class="upload-icon">📁</span>
                                        </button>
                                        <input type="file" id="imageFileInput" accept="image/jpeg,image/jpg,image/png,image/webp" multiple style="display: none;">
                                    </div>
                                </div>
                                <div id="imageUploadStatus" class="upload-status"></div>
                                <div id="imagePreviewContainer" class="image-preview-container"></div>
                            </div>
                        </section>

                        <!-- 行程信息板块 -->
                        <section class="panel compact-card" data-panel="trip-info" role="region" aria-label="行程信息区域" tabindex="0">

                            <div class="section-header">
                                <h3 data-i18n="form.tripInfo">🚗 行程信息</h3>
                            </div>
                            <div class="panel-content compact-inline-layout">
                                <div class="form-group">
                                    <input type="text" id="pickup" aria-label="上车地点" data-i18n="form.pickupPlaceholder" placeholder="上车地点" data-i18n-title="form.pickupTooltip" title="客户上车的地点">
                                </div>

                                <div class="form-group">
                                    <input type="text" id="destination" name="destination" aria-label="目的地" data-i18n="form.dropoffPlaceholder" placeholder="目的地" data-i18n-title="form.dropoffTooltip" title="客户的目的地">
                                </div>

                                <div class="form-group">
                                    <input type="date" id="date" name="date" aria-label="接送日期" data-i18n-title="form.pickupDateTooltip" title="接送日期">
                                </div>

                                <div class="form-group">
                                    <input type="time" id="time" name="time" aria-label="接送时间" data-i18n-title="form.pickupTimeTooltip" title="接送时间">
                                </div>
                            </div>
                        </section>

                        <!-- 特殊需求板块 -->
                        <section class="panel compact-card" data-panel="special-requirements" role="region" aria-label="特殊需求区域" tabindex="0">

                            <div class="section-header">
                                <h3 data-i18n="form.specialRequirements">🔧 特殊需求</h3>
                            </div>
                            <div class="panel-content compact-inline-layout">
                                <div class="form-group">
                                    <div class="checkbox-group-vertical">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="babyChairMain">
                                            <span data-i18n="form.babyChair">儿童座椅</span>
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="tourGuideMain">
                                            <span data-i18n="form.tourGuide">导游服务</span>
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="meetAndGreetMain">
                                            <span data-i18n="form.meetAndGreet">迎接服务</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </div>

                    <!-- 中列：客户信息 -->
                    <div class="column-middle column-mobile">

                        <!-- 客户信息板块 -->
                        <section class="panel compact-card" data-panel="customer-info" role="region" aria-label="客户信息区域" tabindex="0">
                            <div class="section-header">
                                <h3 data-i18n="form.customerInfo">👤 客户信息</h3>
                            </div>
                            <div class="panel-content compact-inline-layout">
                                <div class="form-group">
                                    <input type="text" id="customer_name" name="customer_name" aria-label="客户姓名" data-i18n="form.customerNamePlaceholder" placeholder="客户姓名" data-i18n-title="form.customerNameTooltip" title="客户的姓名">
                                </div>

                                <div class="form-group">
                                    <input type="tel" id="customer_contact" name="customer_contact" aria-label="联系电话" data-i18n="form.customerPhonePlaceholder" placeholder="联系电话" data-i18n-title="form.customerPhoneTooltip" title="客户的联系电话">
                                </div>

                                <div class="form-group">
                                    <input type="email" id="customer_email" name="customer_email" aria-label="客户邮箱" data-i18n="form.customerEmailPlaceholder" placeholder="客户邮箱" data-i18n-title="form.customerEmailTooltip" title="客户的邮箱地址">
                                </div>

                                <div class="form-group">
                                    <input type="text" id="flight_info" name="flight_info" aria-label="航班信息" data-i18n="form.flightInfoPlaceholder" placeholder="航班号/航班信息" data-i18n-title="form.flightInfoTooltip" title="航班号或相关航班信息">
                                </div>
                            </div>
                        </section>

                        <!-- 基本信息板块 -->
                        <section class="panel compact-card" data-panel="basic-info" role="region" aria-label="基本信息区域" tabindex="0">
                            <div class="section-header">
                                <h3 data-i18n="form.basicInfo">📋 基本信息</h3>
                            </div>
                            <div class="panel-content compact-inline-layout">
                                <div class="form-group">
                                    <select id="sub_category_id" name="sub_category_id" aria-label="服务类型" data-i18n="form.serviceTypePlaceholder" data-i18n-title="form.serviceTypeTooltip" title="选择服务类型">
                                        <option value="" data-i18n="form.selectServiceType">请选择服务类型</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <input type="text" id="ota_reference_number" name="ota_reference_number" aria-label="OTA参考号" data-i18n="form.otaReferencePlaceholder" placeholder="OTA平台订单号" data-i18n-title="form.otaReferenceTooltip" title="OTA平台的订单参考号">
                                </div>

                                <div class="form-group">
                                    <select id="ota" aria-label="OTA渠道" data-i18n="form.otaChannelPlaceholder" data-i18n-title="form.otaChannelTooltip" title="选择OTA渠道"></select>
                                </div>

                                <div class="form-group">
                                    <select id="car_type_id" name="car_type_id" aria-label="车型" data-i18n="form.carTypePlaceholder" data-i18n-title="form.carTypeTooltip" title="选择车型">
                                        <option value="" data-i18n="form.selectCarType">请选择车型</option>
                                    </select>
                                </div>

                                <!-- 隐藏的负责人字段 - 根据登录邮箱自动设置 -->
                                <input type="hidden" id="incharge_by_backend_user_id" name="incharge_by_backend_user_id">
                            </div>
                        </section>

                        <!-- 价格信息板块 -->
                        <section class="panel compact-card" data-panel="price-info" role="region" aria-label="价格信息区域" tabindex="0">
                            <div class="section-header">
                                <h3 data-i18n="form.priceInfo">💰 价格信息</h3>
                            </div>
                            <div class="panel-content compact-inline-layout">
                                <div class="form-group" id="ota_price_group">
                                    <div class="compact-price-input">
                                        <input type="number" id="ota_price" name="ota_price" data-i18n-aria="form.otaPrice" aria-label="OTA价格" data-i18n="form.otaPrice" placeholder="OTA价格" data-i18n-title="form.otaPriceTooltip" title="OTA订单价格" step="0.01" min="0">
                                        <select id="currency" name="currency" data-i18n-aria="form.currency" aria-label="货币" data-i18n-title="form.currencyTooltip" title="选择货币">
                                            <option value="MYR">MYR</option>
                                            <option value="USD">USD</option>
                                            <option value="SGD">SGD</option>
                                            <option value="CNY">CNY</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group" id="driver_fee_group">
                                    <input type="number" id="driver_fee" name="driver_fee" data-i18n-aria="form.driverFee" aria-label="司机费用" data-i18n="form.driverFee" placeholder="司机费用" data-i18n-title="form.driverFeeTooltip" title="司机服务费用" step="0.01" min="0">
                                </div>
                            </div>
                        </section>
                    </div>

                    <!-- 右列：服务配置 -->
                    <div class="column-right column-mobile">

                        <!-- 服务配置板块 -->
                        <section class="panel compact-card" data-panel="service-config" role="region" aria-label="服务配置区域" tabindex="0">
                            <div class="section-header">
                                <h3 data-i18n="form.serviceConfig">⚙️ 服务配置</h3>
                            </div>
                            <div class="panel-content compact-inline-layout">
                                <div class="form-group-horizontal">
                                    <div class="form-group">
                                        <input type="number" id="passenger_number" name="passenger_number" min="1" max="20" data-i18n="form.passengerCountPlaceholder" placeholder="乘客人数" data-i18n-title="form.passengerCountTooltip" title="乘客人数">
                                    </div>
                                    <div class="form-group">
                                        <input type="number" id="luggage_number" name="luggage_number" min="0" max="50" data-i18n="form.luggageCountPlaceholder" placeholder="行李件数" data-i18n-title="form.luggageCountTooltip" title="行李件数">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <select id="driving_region_id" name="driving_region_id" data-i18n="form.drivingRegionPlaceholder" data-i18n-title="form.drivingRegionTooltip" title="选择行驶区域">
                                        <option value="" data-i18n="form.selectDrivingRegion">请选择行驶区域</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <div class="language-checkboxes" data-i18n-title="form.languagesTooltip" title="选择语言要求">
                                        <div class="checkbox-item">
                                            <input type="checkbox" id="lang_2" name="languagesIdArray" value="2">
                                            <label for="lang_2">English (EN)</label>
                                        </div>
                                        <div class="checkbox-item">
                                            <input type="checkbox" id="lang_4" name="languagesIdArray" value="4">
                                            <label for="lang_4">Chinese (CN)</label>
                                        </div>
                                        <div class="checkbox-item visually-hidden">
                                            <input type="checkbox" id="lang_5" name="languagesIdArray" value="5">
                                            <label for="lang_5">Paging (PG)</label>
                                        </div>
                                        <div class="checkbox-item">
                                            <input type="checkbox" id="lang_6" name="languagesIdArray" value="6">
                                            <label for="lang_6">Charter (CHARTER)</label>
                                        </div>
                                    </div>
                                </div>


                            </div>
                        </section>

                        <!-- 额外要求板块 -->
                        <section class="panel compact-card" data-panel="extra-requirements" role="region" aria-label="额外要求区域" tabindex="0">
                            <div class="section-header">
                                <h3 data-i18n="form.extraRequirement">📝 额外要求</h3>
                            </div>
                            <div class="panel-content compact-inline-layout">
                                <div class="form-group">
                                    <textarea id="extra_requirement" name="extra_requirement" rows="2" data-i18n="form.extraRequirementPlaceholder" placeholder="其他特殊要求或备注" data-i18n-title="form.extraRequirementTooltip" title="其他特殊要求或备注"></textarea>
                                </div>
                            </div>
                        </section>
                    </div>

                    <!-- 操作按钮区域 - 移入grid内部，跨越两列 -->
                    <section class="action-section grid-span-full">
                        <div class="form-actions">
                            <button type="button" id="returnToMultiOrder" class="btn btn-secondary hidden" data-i18n="multiOrder.returnToMultiOrder">
                                <span>🔢 返回多订单模式</span>
                            </button>
                            <!-- 新增：重置表单按钮 -->
                            <button type="button" id="resetBtn" class="btn btn-outline" data-i18n="actions.resetForm" title="重置表单">
                                ♻️ 重置
                            </button>
                            <button type="button" id="createOrder" class="btn btn-primary">
                                <span data-i18n="actions.createOrder">✅ 创建订单</span>
                            </button>
                        </div>
                    </section>
                </form>





                <!-- 日志控制台 -->
                <!-- 已移除日志控制台前端显示，仅保留后台调试控制台输出 -->
            </div>
        </main>

        <!-- 状态栏 -->
        <footer class="status-bar">
            <div class="status-info">
                <span id="connectionStatus" class="status-item" data-i18n="status.disconnected">🔌 未连接</span>
                <span id="dataStatus" class="status-item" data-i18n="status.waiting">📊 等待数据</span>
                <span id="geminiStatus" class="status-item">🤖 AI服务未配置</span>
                <span id="lastUpdate" class="status-item">⏰ --:--</span>
                <span id="buildVersion" class="status-item" title="当前前端构建版本">🛠️ v--</span>
            </div>
        </footer>

        <!-- 模态框 -->
    <div id="modal" class="modal hidden" aria-hidden="true">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modalTitle"></h3>
                    <button type="button" id="modalClose" class="btn btn-icon">✕</button>
                </div>
                <div id="modalBody" class="modal-body"></div>
            </div>
        </div>

        <!-- 历史订单面板 -->
        <div id="historyPanel" class="history-panel hidden">
            <div class="history-overlay">
                <div class="history-content">
                    <div class="history-header">
                        <h3 data-i18n="history.title">📋 历史订单管理</h3>
                        <div class="history-controls">
                            <button type="button" id="exportHistoryBtn" class="btn btn-outline btn-sm" data-i18n="history.export">导出</button>
                            <button type="button" id="clearHistoryBtn" class="btn btn-outline btn-sm" data-i18n="history.clear">清空</button>
                            <button type="button" id="closeHistoryBtn" class="btn btn-icon" data-i18n-title="common.close">✕</button>
                        </div>
                    </div>

                    <div class="history-search">
                        <div class="search-grid">
                            <div class="search-group">
                                <label for="searchOrderId" data-i18n="history.searchOrderId">订单ID</label>
                                <input type="text" id="searchOrderId" data-i18n="history.searchOrderIdPlaceholder" placeholder="搜索订单ID">
                            </div>
                            <div class="search-group">
                                <label for="searchCustomer" data-i18n="history.searchCustomer">客户姓名</label>
                                <input type="text" id="searchCustomer" data-i18n="history.searchCustomerPlaceholder" placeholder="搜索客户姓名">
                            </div>
                            <div class="search-group">
                                <label for="searchDateFrom" data-i18n="history.searchDateFrom">开始日期</label>
                                <input type="date" id="searchDateFrom">
                            </div>
                            <div class="search-group">
                                <label for="searchDateTo" data-i18n="history.searchDateTo">结束日期</label>
                                <input type="date" id="searchDateTo">
                            </div>
                        </div>
                        <div class="search-actions">
                            <button type="button" id="searchHistoryBtn" class="btn btn-primary btn-sm" data-i18n="history.searchButton">搜索</button>
                            <button type="button" id="resetSearchBtn" class="btn btn-outline btn-sm" data-i18n="history.resetSearch">重置</button>
                        </div>
                    </div>

                    <div class="history-stats">
                        <div class="stats-grid">
                            <div class="stat-item">
                                <span class="stat-label" data-i18n="history.statTotal">总计</span>
                                <span class="stat-value" id="statTotal">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label" data-i18n="history.statToday">今日</span>
                                <span class="stat-value" id="statToday">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label" data-i18n="history.statWeek">本周</span>
                                <span class="stat-value" id="statWeek">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label" data-i18n="history.statMonth">本月</span>
                                <span class="stat-value" id="statMonth">0</span>
                            </div>
                        </div>
                    </div>

                    <div class="history-list">
                        <div class="list-header">
                            <div class="list-title" data-i18n="history.orderList">订单列表</div>
                            <div class="list-count" data-i18n="history.recordCount" data-i18n-params='{"count": "0"}'>共 <span id="listCount">0</span> 条记录</div>
                        </div>
                        <div class="list-container" id="historyListContainer">
                            <div class="empty-state">
                                <div class="empty-icon">📝</div>
                                <div class="empty-text" data-i18n="history.emptyState">暂无历史订单</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 多订单面板已移除 - 现在完全由JavaScript动态创建统一容器 -->
    </div>

    <!-- Modern loader: manifest defines strict order; loader orchestrates execution -->
    <script src="js/core/script-manifest.js"></script>
    <script src="js/core/script-loader.js"></script>
</body>
</html>